import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { ExternalLink, Github, Calendar, Users, Zap, Cloud } from 'lucide-react'

const Projects = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.2 })

  const projects = [
    {
      title: "Attendance Tracking Website",
      description: "A web app for students to check attendance via a secure login system.",
      longDescription: "Built a comprehensive attendance management system with student authentication, percentage calculations, and an intuitive dashboard interface.",
      tech: ["HTML", "CSS", "JavaScript", "Flask"],
      features: ["Student login", "Attendance percentage calculator", "Dashboard UI"],
      icon: <Users className="w-6 h-6" />,
      color: "from-blue-500 to-cyan-500",
      github: "#",
      demo: "#"
    },
    {
      title: "Voxtora AI Tool",
      description: "AI-powered tool to summarize YouTube videos into structured downloadable notes.",
      longDescription: "Developed an intelligent video summarization tool that processes YouTube content and generates comprehensive notes using advanced AI algorithms.",
      tech: ["Python", "Flask", "OpenAI API", "YouTube API"],
      features: ["AI-based note generation", "PDF export", "User input via link"],
      icon: <Zap className="w-6 h-6" />,
      color: "from-purple-500 to-pink-500",
      github: "#",
      demo: "#"
    },
    {
      title: "Weather Forecast Website",
      description: "A real-time weather app with hourly and weekly forecasts.",
      longDescription: "Created a responsive weather application that provides accurate forecasts with location-based data and multiple viewing options.",
      tech: ["React", "OpenWeatherMap API"],
      features: ["Location-based data", "Multiple views (hourly/weekly)", "Live updates"],
      icon: <Cloud className="w-6 h-6" />,
      color: "from-green-500 to-teal-500",
      github: "#",
      demo: "#"
    },
    {
      title: "Personal Portfolio Website",
      description: "A modern, responsive portfolio showcasing my projects and skills.",
      longDescription: "Designed and developed this portfolio website using modern web technologies with smooth animations and responsive design.",
      tech: ["React", "Tailwind CSS", "Framer Motion"],
      features: ["Responsive design", "Smooth animations", "Dark theme", "Contact form"],
      icon: <Calendar className="w-6 h-6" />,
      color: "from-orange-500 to-red-500",
      github: "#",
      demo: "#"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6
      }
    }
  }

  const projectVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="projects" className="py-20 bg-background relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-1/3 left-0 w-96 h-96 bg-accent/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-0 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={projectVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4">
              My <span className="gradient-text">Projects</span>
            </h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              A showcase of my work and the technologies I've mastered
            </p>
            <div className="w-24 h-1 bg-accent mx-auto rounded-full mt-6" />
          </motion.div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={project.title}
                variants={projectVariants}
                whileHover={{ y: -10 }}
                className="glass rounded-2xl overflow-hidden hover-glow group"
              >
                {/* Project Header */}
                <div className={`h-2 bg-gradient-to-r ${project.color}`} />
                
                <div className="p-8">
                  {/* Title and Icon */}
                  <div className="flex items-center mb-4">
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${project.color} text-white mr-4`}>
                      {project.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-white group-hover:text-accent transition-colors duration-300">
                      {project.title}
                    </h3>
                  </div>

                  {/* Description */}
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    {project.description}
                  </p>
                  
                  <p className="text-gray-400 text-sm mb-6 leading-relaxed">
                    {project.longDescription}
                  </p>

                  {/* Tech Stack */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-accent mb-3">Technologies Used:</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.tech.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-3 py-1 bg-accent/20 text-accent rounded-full text-xs font-medium border border-accent/30"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-accent mb-3">Key Features:</h4>
                    <ul className="space-y-1">
                      {project.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="text-gray-400 text-sm flex items-center">
                          <div className="w-1.5 h-1.5 bg-accent rounded-full mr-3" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-4">
                    <motion.a
                      href={project.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors duration-300"
                    >
                      <Github className="w-4 h-4 mr-2" />
                      Code
                    </motion.a>
                    
                    <motion.a
                      href={project.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`flex items-center px-4 py-2 bg-gradient-to-r ${project.color} text-white rounded-lg text-sm font-medium hover:opacity-90 transition-opacity duration-300`}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Demo
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Call to Action */}
          <motion.div 
            variants={projectVariants}
            className="text-center mt-16"
          >
            <div className="glass rounded-2xl p-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-semibold mb-4 text-accent">
                More Projects Coming Soon!
              </h3>
              <p className="text-gray-300 mb-6">
                I'm constantly working on new projects and exploring innovative technologies. 
                Stay tuned for more exciting developments!
              </p>
              <motion.a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-6 py-3 bg-accent text-white rounded-lg font-semibold hover-glow transition-all duration-300"
              >
                <Github className="w-5 h-5 mr-2" />
                View All Projects
              </motion.a>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Projects
