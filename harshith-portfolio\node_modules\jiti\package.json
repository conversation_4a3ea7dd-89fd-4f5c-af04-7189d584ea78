{"name": "jiti", "version": "2.4.2", "description": "Runtime typescript and ESM support for Node.js", "repository": "unjs/jiti", "license": "MIT", "type": "module", "exports": {".": {"import": {"types": "./lib/jiti.d.mts", "default": "./lib/jiti.mjs"}, "require": {"types": "./lib/jiti.d.cts", "default": "./lib/jiti.cjs"}}, "./register": {"types": "./lib/jiti-register.d.mts", "import": "./lib/jiti-register.mjs"}, "./native": {"types": "./lib/jiti.d.mts", "import": "./lib/jiti-native.mjs"}, "./package.json": "./package.json"}, "main": "./lib/jiti.cjs", "module": "./lib/jiti.mjs", "types": "./lib/jiti.d.cts", "typesVersions": {"*": {"register": ["./lib/jiti-register.d.mts"], "native": ["./lib/jiti.d.mts"]}}, "bin": {"jiti": "./lib/jiti-cli.mjs"}, "files": ["lib", "dist", "register.cjs"], "scripts": {"build": "pnpm clean && NODE_ENV=production pnpm webpack", "clean": "rm -rf dist", "dev": "pnpm clean && pnpm webpack --watch", "jiti": "JITI_DEBUG=1 JITI_JSX=1 lib/jiti-cli.mjs", "lint": "eslint . && prettier -c src lib test stubs", "lint:fix": "eslint --fix . && prettier -w src lib test stubs", "prepack": "pnpm build", "release": "pnpm build && pnpm test && changelogen --release --push --publish", "test": "pnpm lint && pnpm test:types && vitest run --coverage && pnpm test:node-register && pnpm test:bun && pnpm test:native", "test:bun": "bun --bun test test/bun", "test:native": "pnpm test:native:bun && pnpm test:native:node && pnpm test:native:deno", "test:native:bun": "bun --bun test test/native/bun.test.ts", "test:native:deno": "deno test -A --no-check test/native/deno.test.ts", "test:native:node": "node --test --experimental-strip-types test/native/node.test.ts", "test:node-register": "JITI_JSX=1 node --test test/node-register.test.mjs", "test:types": "tsc --noEmit"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/helper-module-imports": "^7.25.9", "@babel/helper-module-transforms": "^7.26.0", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-import-assertions": "^7.26.0", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-react-jsx": "^7.25.9", "@babel/plugin-transform-typescript": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@babel/template": "^7.25.9", "@babel/traverse": "^7.26.4", "@babel/types": "^7.26.3", "@types/babel__core": "^7.20.5", "@types/babel__helper-module-imports": "^7.18.3", "@types/babel__helper-plugin-utils": "^7.10.3", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.6", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8", "acorn": "^8.14.0", "babel-plugin-parameter-decorator": "^1.0.16", "changelogen": "^0.5.7", "config": "^3.3.12", "consola": "^3.2.3", "defu": "^6.1.4", "destr": "^2.0.3", "escape-string-regexp": "^5.0.0", "eslint": "^9.17.0", "eslint-config-unjs": "^0.4.2", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "is-installed-globally": "^1.0.0", "mime": "^4.0.4", "mlly": "^1.7.3", "moment-timezone": "^0.5.46", "nano-jsx": "^0.1.0", "pathe": "^1.1.2", "pkg-types": "^1.2.1", "preact": "^10.25.2", "preact-render-to-string": "^6.5.12", "prettier": "^3.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "reflect-metadata": "^0.2.2", "solid-js": "^1.9.3", "std-env": "^3.8.0", "terser-webpack-plugin": "^5.3.11", "tinyexec": "^0.3.1", "ts-loader": "^9.5.1", "typescript": "^5.7.2", "vitest": "^2.1.8", "vue": "^3.5.13", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-license-plugin": "^4.5.0", "yoctocolors": "^2.1.1", "zod": "^3.24.1"}, "packageManager": "pnpm@9.15.0"}