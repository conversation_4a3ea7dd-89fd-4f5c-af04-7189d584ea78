import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { Code, Brain, Target, Zap } from 'lucide-react'

const About = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.3 })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const highlights = [
    {
      icon: <Code className="w-6 h-6" />,
      title: "Development",
      description: "Passionate about creating efficient and scalable solutions"
    },
    {
      icon: <Brain className="w-6 h-6" />,
      title: "Machine Learning",
      description: "Exploring AI to build intelligent applications"
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Problem Solving",
      description: "Love tackling complex DSA challenges"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Innovation",
      description: "Always eager to learn new technologies"
    }
  ]

  return (
    <section id="about" className="py-20 bg-background relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-0 w-72 h-72 bg-accent/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4">
              About <span className="gradient-text">Me</span>
            </h2>
            <div className="w-24 h-1 bg-accent mx-auto rounded-full" />
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="glass rounded-2xl p-8">
                <h3 className="text-2xl font-semibold mb-4 text-accent">
                  My Journey
                </h3>
                <p className="text-gray-300 leading-relaxed mb-6">
                  I'm a B.Tech student eager to explore real-world opportunities as a developer. 
                  I'm deeply interested in machine learning and love solving DSA problems. 
                  My passion lies in creating innovative solutions that bridge the gap between 
                  theoretical knowledge and practical applications.
                </p>
                <p className="text-gray-300 leading-relaxed">
                  While I may not have professional work experience yet, I've dedicated countless 
                  hours to building projects, learning new technologies, and honing my skills. 
                  I believe in continuous learning and am always excited to take on new challenges 
                  that push me to grow as a developer.
                </p>
              </div>

              {/* Stats */}
              <motion.div 
                variants={itemVariants}
                className="grid grid-cols-2 gap-4"
              >
                <div className="glass rounded-xl p-6 text-center hover-glow">
                  <div className="text-3xl font-bold text-accent mb-2">10+</div>
                  <div className="text-gray-400 text-sm">Projects Built</div>
                </div>
                <div className="glass rounded-xl p-6 text-center hover-glow">
                  <div className="text-3xl font-bold text-accent mb-2">5+</div>
                  <div className="text-gray-400 text-sm">Technologies</div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right side - Highlights grid */}
            <motion.div variants={itemVariants} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {highlights.map((highlight, index) => (
                  <motion.div
                    key={highlight.title}
                    variants={itemVariants}
                    whileHover={{ scale: 1.05 }}
                    className="glass rounded-xl p-6 hover-glow group cursor-pointer"
                  >
                    <div className="text-accent mb-4 group-hover:scale-110 transition-transform duration-300">
                      {highlight.icon}
                    </div>
                    <h4 className="text-lg font-semibold mb-2 text-white">
                      {highlight.title}
                    </h4>
                    <p className="text-gray-400 text-sm leading-relaxed">
                      {highlight.description}
                    </p>
                  </motion.div>
                ))}
              </div>

              {/* Additional info card */}
              <motion.div 
                variants={itemVariants}
                className="glass rounded-xl p-6 border-l-4 border-accent"
              >
                <h4 className="text-lg font-semibold mb-3 text-accent">
                  What Drives Me
                </h4>
                <p className="text-gray-300 text-sm leading-relaxed">
                  I'm motivated by the endless possibilities that technology offers. 
                  From solving complex algorithms to building user-friendly applications, 
                  every project is an opportunity to learn something new and make a positive impact.
                </p>
              </motion.div>
            </motion.div>
          </div>

          {/* Call to action */}
          <motion.div 
            variants={itemVariants}
            className="text-center mt-16"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                const element = document.querySelector('#contact')
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' })
                }
              }}
              className="px-8 py-3 bg-gradient-to-r from-accent to-primary text-white rounded-lg font-semibold hover-glow transition-all duration-300"
            >
              Let's Connect
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
