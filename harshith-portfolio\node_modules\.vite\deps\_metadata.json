{"hash": "245cf689", "configHash": "c22a44fa", "lockfileHash": "2e55db85", "browserHash": "bcb3a6e2", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "2ecccaba", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "ca79bd7b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a1e8d954", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "0cae1d08", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "a544a623", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "8f486513", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "79789d11", "needsInterop": true}, "react-icons/si": {"src": "../../react-icons/si/index.mjs", "file": "react-icons_si.js", "fileHash": "94eb248f", "needsInterop": false}}, "chunks": {"chunk-2ZET3HRN": {"file": "chunk-2ZET3HRN.js"}, "chunk-S3Z6QACX": {"file": "chunk-S3Z6QACX.js"}, "chunk-IYDKXRZQ": {"file": "chunk-IYDKXRZQ.js"}}}