import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { 
  SiJava, 
  SiPython, 
  SiHtml5, 
  SiCss3, 
  SiReact, 
  SiTailwindcss, 
  SiFlask, 
  SiMysql,
  SiNumpy,
  SiPandas,
  SiSelenium,
  SiScikitlearn
} from 'react-icons/si'

const Skills = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.2 })

  const skillCategories = [
    {
      title: "Languages",
      skills: [
        { name: "Java", icon: <SiJava className="w-8 h-8" />, color: "text-orange-500" },
        { name: "Python", icon: <SiPython className="w-8 h-8" />, color: "text-blue-500" },
        { name: "HTML", icon: <SiHtml5 className="w-8 h-8" />, color: "text-orange-600" },
        { name: "CSS", icon: <SiCss3 className="w-8 h-8" />, color: "text-blue-600" },
      ]
    },
    {
      title: "Frontend",
      skills: [
        { name: "React", icon: <SiReact className="w-8 h-8" />, color: "text-cyan-400" },
        { name: "Tailwind CSS", icon: <SiTailwindcss className="w-8 h-8" />, color: "text-teal-400" },
      ]
    },
    {
      title: "Backend",
      skills: [
        { name: "Flask", icon: <SiFlask className="w-8 h-8" />, color: "text-gray-300" },
      ]
    },
    {
      title: "Database",
      skills: [
        { name: "MySQL", icon: <SiMysql className="w-8 h-8" />, color: "text-blue-400" },
      ]
    },
    {
      title: "Python Libraries",
      skills: [
        { name: "NumPy", icon: <SiNumpy className="w-8 h-8" />, color: "text-blue-300" },
        { name: "Pandas", icon: <SiPandas className="w-8 h-8" />, color: "text-purple-400" },
        { name: "Selenium", icon: <SiSelenium className="w-8 h-8" />, color: "text-green-400" },
        { name: "Scikit-learn", icon: <SiScikitlearn className="w-8 h-8" />, color: "text-orange-400" },
      ]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.6
      }
    }
  }

  const categoryVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const skillVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="skills" className="py-20 bg-gradient-to-b from-background to-card/20 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-1/4 w-64 h-64 bg-accent/10 rounded-full blur-3xl" />
        <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-primary/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          <motion.div variants={categoryVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4">
              My <span className="gradient-text">Skills</span>
            </h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Technologies and tools I work with to bring ideas to life
            </p>
            <div className="w-24 h-1 bg-accent mx-auto rounded-full mt-6" />
          </motion.div>

          {/* Skills Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {skillCategories.map((category, categoryIndex) => (
              <motion.div
                key={category.title}
                variants={categoryVariants}
                className="glass rounded-2xl p-6 hover-glow group"
              >
                <h3 className="text-xl font-semibold mb-6 text-accent group-hover:text-white transition-colors duration-300">
                  {category.title}
                </h3>
                
                <div className="grid grid-cols-2 gap-4">
                  {category.skills.map((skill, skillIndex) => (
                    <motion.div
                      key={skill.name}
                      variants={skillVariants}
                      whileHover={{ 
                        scale: 1.1,
                        rotate: [0, -5, 5, 0],
                        transition: { duration: 0.3 }
                      }}
                      className="flex flex-col items-center p-4 rounded-xl bg-background/50 hover:bg-background/80 transition-all duration-300 cursor-pointer group/skill"
                    >
                      <div className={`${skill.color} mb-3 group-hover/skill:scale-110 transition-transform duration-300`}>
                        {skill.icon}
                      </div>
                      <span className="text-sm font-medium text-gray-300 group-hover/skill:text-white transition-colors duration-300 text-center">
                        {skill.name}
                      </span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Additional Info */}
          <motion.div 
            variants={categoryVariants}
            className="mt-16 text-center"
          >
            <div className="glass rounded-2xl p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-semibold mb-4 text-accent">
                Continuous Learning
              </h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                I'm always expanding my skill set and staying up-to-date with the latest technologies. 
                Currently exploring advanced machine learning concepts, cloud technologies, and modern web development frameworks.
              </p>
              <div className="flex flex-wrap justify-center gap-3">
                {["Docker", "AWS", "MongoDB", "Node.js", "TypeScript"].map((tech, index) => (
                  <motion.span
                    key={tech}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.4, delay: 0.8 + index * 0.1 }}
                    className="px-4 py-2 bg-accent/20 text-accent rounded-full text-sm font-medium border border-accent/30"
                  >
                    {tech}
                  </motion.span>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Skills
